import 'package:flutter/material.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension RichTextColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// Enum for different display modes of the rich text widget
enum RichTextDisplayMode {
  compact,    // Default compact state
  hover,      // Expanded with toolbar on hover
  editing,    // Active editing state
  expanded    // Full dialog state
}

/// A compact rich text widget that expands on hover and supports full editing in dialog
class RichTextWidget extends StatefulWidget {
  // Content properties
  final String initialText;
  final Map<String, dynamic>? jsonData;
  final String displayKey;
  final bool isEditable;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color hoverColor;
  final double fontSize;
  final FontWeight fontWeight;
  final String? fontFamily;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Layout properties
  final double? width;
  final double compactHeight;
  final double expandedHeight;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;
  final bool hasShadow;
  final double elevation;

  // Toolbar properties
  final bool showToolbar;
  final bool showBoldButton;
  final bool showItalicButton;
  final bool showUnderlineButton;
  final bool showStrikethroughButton;
  final bool showColorPicker;
  final bool showFontSizePicker;
  final bool showAlignmentButtons;
  final bool showBulletList;
  final bool showNumberedList;
  final bool showLinkButton;
  final bool showClearFormattingButton;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool selectable;
  final bool autofocus;
  final Duration animationDuration;

  // Callbacks
  final Function(String)? onTextChanged;
  final Function()? onTap;
  final Function()? onExpand;
  final Function(bool)? onHover;
  final Function(bool)? onFocus;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;

  const RichTextWidget({
    super.key,
    this.initialText = 'Class',
    this.jsonData,
    this.displayKey = 'text',
    this.isEditable = true,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.hoverColor = const Color(0xFFF5F5F5),
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.fontFamily,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.width,
    this.compactHeight = 20.0,
    this.expandedHeight = 120.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
    this.margin = EdgeInsets.zero,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.showToolbar = true,
    this.showBoldButton = true,
    this.showItalicButton = true,
    this.showUnderlineButton = true,
    this.showStrikethroughButton = true,
    this.showColorPicker = true,
    this.showFontSizePicker = true,
    this.showAlignmentButtons = true,
    this.showBulletList = true,
    this.showNumberedList = true,
    this.showLinkButton = true,
    this.showClearFormattingButton = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.selectable = true,
    this.autofocus = false,
    this.animationDuration = const Duration(milliseconds: 200),
    this.onTextChanged,
    this.onTap,
    this.onExpand,
    this.onHover,
    this.onFocus,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
  });

  /// Creates a RichTextWidget from a JSON map
  factory RichTextWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color textColor = Colors.black;
    if (json.containsKey('textColor')) {
      textColor = _parseColor(json['textColor']);
    }

    Color backgroundColor = Colors.white;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color hoverColor = const Color(0xFFF5F5F5);
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color borderColor = Colors.grey;
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    // Parse font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json.containsKey('fontWeight')) {
      fontWeight = _parseFontWeight(json['fontWeight']);
    }

    // Parse text alignment
    TextAlign textAlign = TextAlign.start;
    if (json.containsKey('textAlign')) {
      textAlign = _parseTextAlign(json['textAlign']);
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0);
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = EdgeInsets.zero;
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    return RichTextWidget(
      initialText: json['initialText'] as String? ?? 'Class',
      jsonData: json['jsonData'] as Map<String, dynamic>?,
      displayKey: json['displayKey'] as String? ?? 'text',
      isEditable: json['isEditable'] as bool? ?? true,
      textColor: textColor,
      backgroundColor: backgroundColor,
      hoverColor: hoverColor,
      fontSize: json['fontSize'] != null ? (json['fontSize'] as num).toDouble() : 14.0,
      fontWeight: fontWeight,
      fontFamily: json['fontFamily'] as String?,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      textAlign: textAlign,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      compactHeight: json['compactHeight'] != null ? (json['compactHeight'] as num).toDouble() : 20.0,
      expandedHeight: json['expandedHeight'] != null ? (json['expandedHeight'] as num).toDouble() : 120.0,
      padding: padding,
      margin: margin,
      borderRadius: json['borderRadius'] != null ? (json['borderRadius'] as num).toDouble() : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      borderColor: borderColor,
      borderWidth: json['borderWidth'] != null ? (json['borderWidth'] as num).toDouble() : 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: json['elevation'] != null ? (json['elevation'] as num).toDouble() : 2.0,
      showToolbar: json['showToolbar'] as bool? ?? true,
      showBoldButton: json['showBoldButton'] as bool? ?? true,
      showItalicButton: json['showItalicButton'] as bool? ?? true,
      showUnderlineButton: json['showUnderlineButton'] as bool? ?? true,
      showStrikethroughButton: json['showStrikethroughButton'] as bool? ?? true,
      showColorPicker: json['showColorPicker'] as bool? ?? true,
      showFontSizePicker: json['showFontSizePicker'] as bool? ?? true,
      showAlignmentButtons: json['showAlignmentButtons'] as bool? ?? true,
      showBulletList: json['showBulletList'] as bool? ?? true,
      showNumberedList: json['showNumberedList'] as bool? ?? true,
      showLinkButton: json['showLinkButton'] as bool? ?? true,
      showClearFormattingButton: json['showClearFormattingButton'] as bool? ?? true,
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      selectable: json['selectable'] as bool? ?? true,
      autofocus: json['autofocus'] as bool? ?? false,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      jsonCallbacks: json.containsKey('callbacks') ? json['callbacks'] as Map<String, dynamic> : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex';
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return Colors.black;
        }
      }
    } else if (colorValue is Map) {
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a = colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Colors.black;
  }

  /// Parses font weight from string or int
  static FontWeight _parseFontWeight(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'thin': return FontWeight.w100;
        case 'extralight': return FontWeight.w200;
        case 'light': return FontWeight.w300;
        case 'regular': return FontWeight.w400;
        case 'medium': return FontWeight.w500;
        case 'semibold': return FontWeight.w600;
        case 'bold': return FontWeight.w700;
        case 'extrabold': return FontWeight.w800;
        case 'black': return FontWeight.w900;
        default: return FontWeight.normal;
      }
    } else if (value is int) {
      switch (value) {
        case 100: return FontWeight.w100;
        case 200: return FontWeight.w200;
        case 300: return FontWeight.w300;
        case 400: return FontWeight.w400;
        case 500: return FontWeight.w500;
        case 600: return FontWeight.w600;
        case 700: return FontWeight.w700;
        case 800: return FontWeight.w800;
        case 900: return FontWeight.w900;
        default: return FontWeight.normal;
      }
    }
    return FontWeight.normal;
  }

  /// Parses text alignment from string
  static TextAlign _parseTextAlign(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'left': return TextAlign.left;
        case 'center': return TextAlign.center;
        case 'right': return TextAlign.right;
        case 'justify': return TextAlign.justify;
        case 'start': return TextAlign.start;
        case 'end': return TextAlign.end;
        default: return TextAlign.start;
      }
    }
    return TextAlign.start;
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') || value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal: value.containsKey('horizontal') ? (value['horizontal'] as num).toDouble() : 0.0,
          vertical: value.containsKey('vertical') ? (value['vertical'] as num).toDouble() : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom') ? (value['bottom'] as num).toDouble() : 0.0,
        );
      }
    }
    return const EdgeInsets.all(0.0);
  }

  @override
  State<RichTextWidget> createState() => _RichTextWidgetState();
}

/// Text formatting information for a character range
class TextFormatting {
  final bool isBold;
  final bool isItalic;
  final bool isUnderlined;
  final bool isStrikethrough;
  final Color color;
  final double fontSize;

  const TextFormatting({
    this.isBold = false,
    this.isItalic = false,
    this.isUnderlined = false,
    this.isStrikethrough = false,
    this.color = Colors.black,
    this.fontSize = 14.0,
  });

  TextFormatting copyWith({
    bool? isBold,
    bool? isItalic,
    bool? isUnderlined,
    bool? isStrikethrough,
    Color? color,
    double? fontSize,
  }) {
    return TextFormatting(
      isBold: isBold ?? this.isBold,
      isItalic: isItalic ?? this.isItalic,
      isUnderlined: isUnderlined ?? this.isUnderlined,
      isStrikethrough: isStrikethrough ?? this.isStrikethrough,
      color: color ?? this.color,
      fontSize: fontSize ?? this.fontSize,
    );
  }

  TextStyle toTextStyle({String? fontFamily}) {
    List<TextDecoration> decorations = [];
    if (isUnderlined) decorations.add(TextDecoration.underline);
    if (isStrikethrough) decorations.add(TextDecoration.lineThrough);

    return TextStyle(
      fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
      fontStyle: isItalic ? FontStyle.italic : FontStyle.normal,
      decoration: decorations.isEmpty ? TextDecoration.none : TextDecoration.combine(decorations),
      color: color,
      fontSize: fontSize,
      fontFamily: fontFamily,
    );
  }
}

class _RichTextWidgetState extends State<RichTextWidget> with TickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _heightAnimation;
  late Animation<Color?> _colorAnimation;

  RichTextDisplayMode _currentMode = RichTextDisplayMode.compact;
  bool _isHovered = false;
  bool _isFocused = false;
  String _currentText = '';

  // Rich text formatting
  List<TextFormatting> _textFormatting = [];
  
  // Current formatting state for new text
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderlined = false;
  bool _isStrikethrough = false;
  Color _currentColor = Colors.black;
  double _currentFontSize = 14.0;
  TextAlign _currentTextAlign = TextAlign.start;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    
    // Initialize text from JSON data or initial text
    _currentText = _getDisplayText();
    _controller = TextEditingController(text: _currentText);
    _focusNode = FocusNode();
    
    // Initialize formatting for each character
    _initializeTextFormatting();
    
    // Initialize animation controller
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    // Initialize animations
    _heightAnimation = Tween<double>(
      begin: widget.compactHeight,
      end: widget.expandedHeight,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _colorAnimation = ColorTween(
      begin: widget.backgroundColor,
      end: widget.hoverColor,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Initialize formatting state
    _currentColor = widget.isDarkTheme ? Colors.white : widget.textColor;
    _currentFontSize = widget.fontSize;
    _currentTextAlign = widget.textAlign;

    // Add listeners
    _controller.addListener(_handleTextChange);
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  /// Initialize formatting for each character in the text
  void _initializeTextFormatting() {
    _textFormatting = List.generate(_currentText.length, (index) => TextFormatting(
      color: widget.isDarkTheme ? Colors.white : widget.textColor,
      fontSize: widget.fontSize,
    ));
  }

  /// Apply formatting to selected text
  void _applyFormattingToSelection() {
    final selection = _controller.selection;
    if (!selection.isValid || selection.start == selection.end) {
      return; // No selection
    }

    final start = selection.start;
    final end = selection.end;

    setState(() {
      for (int i = start; i < end && i < _textFormatting.length; i++) {
        _textFormatting[i] = _textFormatting[i].copyWith(
          isBold: _isBold,
          isItalic: _isItalic,
          isUnderlined: _isUnderlined,
          isStrikethrough: _isStrikethrough,
          color: _currentColor,
          fontSize: _currentFontSize,
        );
      }
    });
  }

  /// Update formatting arrays when text changes
  void _updateFormattingOnTextChange(String oldText, String newText) {
    if (newText.length > oldText.length) {
      // Text was added
      final insertPosition = _controller.selection.start;
      final insertedLength = newText.length - oldText.length;
      
      // Create formatting for new characters
      final newFormatting = TextFormatting(
        isBold: _isBold,
        isItalic: _isItalic,
        isUnderlined: _isUnderlined,
        isStrikethrough: _isStrikethrough,
        color: _currentColor,
        fontSize: _currentFontSize,
      );
      
      // Insert new formatting at the correct position
      for (int i = 0; i < insertedLength; i++) {
        _textFormatting.insert(insertPosition + i, newFormatting);
      }
    } else if (newText.length < oldText.length) {
      // Text was removed
      final deletePosition = _controller.selection.start;
      final deletedLength = oldText.length - newText.length;
      
      // Remove formatting for deleted characters
      for (int i = 0; i < deletedLength && deletePosition < _textFormatting.length; i++) {
        _textFormatting.removeAt(deletePosition);
      }
    }
  }

  /// Build rich text with formatting
  Widget _buildRichText() {
    if (_textFormatting.isEmpty || _currentText.isEmpty) {
      return Text(
        _currentText.isEmpty ? 'Class' : _currentText,
        style: TextStyle(
          color: widget.isDarkTheme ? Colors.white : widget.textColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
          fontFamily: widget.fontFamily,
        ),
        textAlign: _currentTextAlign,
      );
    }

    List<TextSpan> spans = [];
    TextFormatting? currentFormatting;
    String currentText = '';

    for (int i = 0; i < _currentText.length; i++) {
      final char = _currentText[i];
      final formatting = i < _textFormatting.length ? _textFormatting[i] : TextFormatting();

      if (currentFormatting == null || !_formattingEquals(currentFormatting, formatting)) {
        // Add previous span if exists
        if (currentText.isNotEmpty) {
          spans.add(TextSpan(
            text: currentText,
            style: currentFormatting?.toTextStyle(fontFamily: widget.fontFamily),
          ));
        }
        
        // Start new span
        currentFormatting = formatting;
        currentText = char;
      } else {
        // Continue current span
        currentText += char;
      }
    }

    // Add final span
    if (currentText.isNotEmpty) {
      spans.add(TextSpan(
        text: currentText,
        style: currentFormatting?.toTextStyle(fontFamily: widget.fontFamily),
      ));
    }

    return RichText(
      text: TextSpan(children: spans),
      textAlign: _currentTextAlign,
    );
  }

  /// Check if two formatting objects are equal
  bool _formattingEquals(TextFormatting a, TextFormatting b) {
    return a.isBold == b.isBold &&
           a.isItalic == b.isItalic &&
           a.isUnderlined == b.isUnderlined &&
           a.isStrikethrough == b.isStrikethrough &&
           a.color == b.color &&
           a.fontSize == b.fontSize;
  }

  @override
  void didUpdateWidget(RichTextWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update text if JSON data changed
    if (widget.jsonData != oldWidget.jsonData) {
      final newText = _getDisplayText();
      if (newText != _currentText) {
        _currentText = newText;
        _controller.text = newText;
      }
    }

    // Update callback state if provided
    if (widget.callbackState != null && widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_handleTextChange);
    _controller.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// Gets the display text from JSON data or falls back to initial text
  String _getDisplayText() {
    if (widget.jsonData != null) {
      return widget.jsonData![widget.displayKey]?.toString() ?? widget.initialText;
    }
    return widget.initialText;
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  void _handleTextChange() {
    final newText = _controller.text;
    if (_currentText != newText) {
      final oldText = _currentText;
      setState(() {
        _updateFormattingOnTextChange(oldText, newText);
        _currentText = newText;
      });

      if (widget.onTextChanged != null) {
        widget.onTextChanged!(newText);
      }

      if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onTextChanged')) {
        _executeJsonCallback('onTextChanged', newText);
      }
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused) {
        _currentMode = RichTextDisplayMode.editing;
      } else if (!_isHovered) {
        _currentMode = RichTextDisplayMode.compact;
        _animationController.reverse();
      }
    });

    if (widget.onFocus != null) {
      widget.onFocus!(_isFocused);
    }
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
      if (isHovered && _currentMode == RichTextDisplayMode.compact) {
        _currentMode = RichTextDisplayMode.hover;
        _animationController.forward();
      } else if (!isHovered && !_isFocused && _currentMode == RichTextDisplayMode.hover) {
        _currentMode = RichTextDisplayMode.compact;
        _animationController.reverse();
      }
    });

    if (widget.onHover != null) {
      widget.onHover!(isHovered);
    }
  }

  void _onTap() {
    if (!widget.isDisabled && widget.isEditable) {
      _focusNode.requestFocus();
    }

    if (widget.onTap != null) {
      widget.onTap!();
    }

    if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onTap')) {
      _executeJsonCallback('onTap');
    }
  }

  void _onExpand() {
    _showExpandedEditor();

    if (widget.onExpand != null) {
      widget.onExpand!();
    }

    if (widget.useJsonCallbacks && widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onExpand')) {
      _executeJsonCallback('onExpand');
    }
  }

  void _showExpandedEditor() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _ExpandedRichTextEditor(
          initialText: _currentText,
          onTextChanged: (text) {
            setState(() {
              _currentText = text;
              _controller.text = text;
            });
            if (widget.onTextChanged != null) {
              widget.onTextChanged!(text);
            }
          },
          isDarkTheme: widget.isDarkTheme,
          fontSize: widget.fontSize,
          fontFamily: widget.fontFamily,
          showBoldButton: widget.showBoldButton,
          showItalicButton: widget.showItalicButton,
          showUnderlineButton: widget.showUnderlineButton,
          showStrikethroughButton: widget.showStrikethroughButton,
          showColorPicker: widget.showColorPicker,
          showFontSizePicker: widget.showFontSizePicker,
          showAlignmentButtons: widget.showAlignmentButtons,
          showBulletList: widget.showBulletList,
          showNumberedList: widget.showNumberedList,
          showLinkButton: widget.showLinkButton,
          showClearFormattingButton: widget.showClearFormattingButton,
        );
      },
    );
  }

  Widget _buildCompactContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            _currentText.isEmpty ? 'Class' : _currentText,
            style: TextStyle(
              color: widget.isDarkTheme ? Colors.white : widget.textColor,
              fontSize: 12.0, // Smaller font size for compact mode
              fontWeight: widget.fontWeight,
              fontFamily: widget.fontFamily,
              height: 1.0, // Tight line height
            ),
            textAlign: widget.textAlign,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        const SizedBox(width: 4.0),
        Icon(
          Icons.edit,
          size: 12.0, // Smaller icon
          color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade600,
        ),
      ],
    );
  }

  Widget _buildHoverContent() {
    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: widget.isEditable && !widget.isReadOnly
                    ? TextField(
                        controller: _controller,
                        focusNode: _focusNode,
                        style: TextStyle(
                          color: widget.isDarkTheme ? Colors.white : widget.textColor,
                          fontSize: widget.fontSize,
                          fontWeight: widget.fontWeight,
                          fontFamily: widget.fontFamily,
                        ),
                        textAlign: widget.textAlign,
                        decoration: const InputDecoration(
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                        ),
                        maxLines: null,
                        enabled: !widget.isDisabled,
                      )
                    : Text(
                        _currentText,
                        style: TextStyle(
                          color: widget.isDarkTheme ? Colors.white : widget.textColor,
                          fontSize: widget.fontSize,
                          fontWeight: widget.fontWeight,
                          fontFamily: widget.fontFamily,
                        ),
                        textAlign: widget.textAlign,
                      ),
              ),
            ],
          ),
        ),
        if (widget.showToolbar && widget.isEditable && !widget.isDisabled)
          _buildBottomToolbar(),
      ],
    );
  }

  Widget _buildBottomToolbar() {
    return Container(
      height: 40.0,
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
      decoration: BoxDecoration(
        color: widget.isDarkTheme ? Colors.grey.shade800 : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(4.0),
        border: Border.all(
          color: widget.isDarkTheme ? Colors.grey.shade600 : Colors.grey.shade300,
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          // Font size dropdown
          if (widget.showFontSizePicker)
            _buildFontSizeDropdown(),
    
            if (widget.showColorPicker)
            _buildColorPickerButton(),
          // Basic formatting buttons
          if (widget.showBoldButton)
            _buildToolbarButton(Icons.format_bold, _isBold, _toggleBold, 'Bold'),
          if (widget.showItalicButton)
            _buildToolbarButton(Icons.format_italic, _isItalic, _toggleItalic, 'Italic'),
          if (widget.showUnderlineButton)
            _buildToolbarButton(Icons.format_underlined, _isUnderlined, _toggleUnderline, 'Underline'),
          // if (widget.showStrikethroughButton)
          //   _buildToolbarButton(Icons.strikethrough_s, _isStrikethrough, _toggleStrikethrough, 'Strikethrough'),
          
          // if (widget.showBoldButton || widget.showItalicButton || widget.showUnderlineButton || widget.showStrikethroughButton)
          //   _buildVerticalDivider(),
          
          // Text color picker
          if (widget.showColorPicker)
            _buildColorPickerButton(),
          if (widget.showColorPicker)
            _buildVerticalDivider(),
          
          // Alignment buttons
          // if (widget.showAlignmentButtons)
          //   _buildAlignmentButtons(),
          // if (widget.showAlignmentButtons)
          //   _buildVerticalDivider(),
          
          // List buttons
          // if (widget.showBulletList)
          //   _buildToolbarButton(Icons.format_list_bulleted, false, _toggleBulletList, 'Bullet List'),
          // if (widget.showNumberedList)
          //   _buildToolbarButton(Icons.format_list_numbered, false, _toggleNumberedList, 'Numbered List'),
          
          // if (widget.showBulletList || widget.showNumberedList)
          //   _buildVerticalDivider(),
          
          // // Link button
          // if (widget.showLinkButton)
          //   _buildToolbarButton(Icons.link, false, _addLink, 'Add Link'),
          
          // // Clear formatting
          // if (widget.showClearFormattingButton)
          //   _buildToolbarButton(Icons.format_clear, false, _clearFormatting, 'Clear Formatting'),
          
          const Spacer(),
          
          // Expand button
          IconButton(
            icon: const Icon(Icons.open_in_full),
            onPressed: _onExpand,
            iconSize: 14.0,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 28.0,
              minHeight: 28.0,
            ),
            tooltip: 'Expand Editor',
            color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton(IconData icon, bool isActive, VoidCallback onPressed, String tooltip) {
    return IconButton(
      icon: Icon(icon),
      onPressed: onPressed,
      color: isActive 
          ? Colors.blue 
          : (widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700),
      iconSize: 14.0,
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(
        minWidth: 28.0,
        minHeight: 28.0,
      ),
      tooltip: tooltip,
      style: ButtonStyle(
        backgroundColor: isActive 
            ? WidgetStateProperty.all(Colors.blue.withAlpha(51))
            : null,
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
        ),
      ),
    );
  }

  void _toggleBold() {
    setState(() {
      _isBold = !_isBold;
      _applyFormattingToSelection();
    });
  }

  void _toggleItalic() {
    setState(() {
      _isItalic = !_isItalic;
      _applyFormattingToSelection();
    });
  }

  void _toggleUnderline() {
    setState(() {
      _isUnderlined = !_isUnderlined;
      _applyFormattingToSelection();
    });
  }

  void _toggleStrikethrough() {
    setState(() {
      _isStrikethrough = !_isStrikethrough;
      _applyFormattingToSelection();
    });
  }

  void _changeAlignment(TextAlign align) {
    setState(() {
      _currentTextAlign = align;
    });
  }

  void _toggleBulletList() {
    setState(() {
      // Toggle bullet list functionality
    });
  }

  void _toggleNumberedList() {
    setState(() {
      // Toggle numbered list functionality
    });
  }

  void _addLink() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Link'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: 'Text',
                hintText: 'Enter link text',
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              decoration: const InputDecoration(
                labelText: 'URL',
                hintText: 'Enter URL',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Add link implementation would go here
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _clearFormatting() {
    setState(() {
      _isBold = false;
      _isItalic = false;
      _isUnderlined = false;
      _isStrikethrough = false;
      _currentColor = widget.isDarkTheme ? Colors.white : widget.textColor;
      _currentFontSize = widget.fontSize;
      _currentTextAlign = widget.textAlign;
    });
  }

  Widget _buildVerticalDivider() {
    return Container(
      width: 1.0,
      height: 20.0,
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      color: widget.isDarkTheme ? Colors.grey.shade600 : Colors.grey.shade400,
    );
  }

  Widget _buildFontSizeDropdown() {
    return Container(
      height: 28.0,
      padding: const EdgeInsets.symmetric(horizontal: 6.0),
      decoration: BoxDecoration(
        border: Border.all(
          color: widget.isDarkTheme ? Colors.grey.shade600 : Colors.grey.shade400,
          width: 0.5,
        ),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<double>(
          value: _currentFontSize,
          isDense: true,
          style: TextStyle(
            fontSize: 12.0,
            color: widget.isDarkTheme ? Colors.white : Colors.black,
          ),
          dropdownColor: widget.isDarkTheme ? Colors.grey.shade800 : Colors.white,
          items: [8.0, 9.0, 10.0, 11.0, 12.0, 14.0, 16.0, 18.0, 20.0, 24.0, 28.0, 32.0].map((size) {
            return DropdownMenuItem(
              value: size,
              child: Text('${size.toInt()}'),
            );
          }).toList(),
          onChanged: (size) {
            if (size != null) {
              setState(() {
                _currentFontSize = size;
                _applyFormattingToSelection();
              });
            }
          },
        ),
      ),
    );
  }

  Widget _buildColorPickerButton() {
    return PopupMenuButton<Color>(
      icon: Stack(
        children: [
          Icon(
            Icons.format_color_text,
            size: 14.0,
            color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 3.0,
              decoration: BoxDecoration(
                color: _currentColor,
                borderRadius: BorderRadius.circular(1.0),
              ),
            ),
          ),
        ],
      ),
      tooltip: 'Text Color',
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(
        minWidth: 28.0,
        minHeight: 28.0,
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: Colors.black,
          child: _ColorItem(color: Colors.black, name: 'Black'),
        ),
        PopupMenuItem(
          value: Colors.red,
          child: _ColorItem(color: Colors.red, name: 'Red'),
        ),
        PopupMenuItem(
          value: Colors.blue,
          child: _ColorItem(color: Colors.blue, name: 'Blue'),
        ),
        PopupMenuItem(
          value: Colors.green,
          child: _ColorItem(color: Colors.green, name: 'Green'),
        ),
        PopupMenuItem(
          value: Colors.orange,
          child: _ColorItem(color: Colors.orange, name: 'Orange'),
        ),
        PopupMenuItem(
          value: Colors.purple,
          child: _ColorItem(color: Colors.purple, name: 'Purple'),
        ),
        PopupMenuItem(
          value: Colors.brown,
          child: _ColorItem(color: Colors.brown, name: 'Brown'),
        ),
        PopupMenuItem(
          value: Colors.grey,
          child: _ColorItem(color: Colors.grey, name: 'Grey'),
        ),
      ],
      onSelected: (color) {
        setState(() {
          _currentColor = color;
          _applyFormattingToSelection();
        });
      },
    );
  }

  Widget _buildAlignmentButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildToolbarButton(
          Icons.format_align_left, 
          _currentTextAlign == TextAlign.left, 
          () => _changeAlignment(TextAlign.left),
          'Align Left'
        ),
        _buildToolbarButton(
          Icons.format_align_center, 
          _currentTextAlign == TextAlign.center, 
          () => _changeAlignment(TextAlign.center),
          'Align Center'
        ),
        _buildToolbarButton(
          Icons.format_align_right, 
          _currentTextAlign == TextAlign.right, 
          () => _changeAlignment(TextAlign.right),
          'Align Right'
        ),
        _buildToolbarButton(
          Icons.format_align_justify, 
          _currentTextAlign == TextAlign.justify, 
          () => _changeAlignment(TextAlign.justify),
          'Justify'
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = widget.isDarkTheme ? Colors.grey.shade900 : widget.backgroundColor;
    final effectiveBorderColor = widget.isDarkTheme ? Colors.grey.shade600 : widget.borderColor;
    final effectiveShadowColor = widget.isDarkTheme ? Colors.black : Colors.black26;

    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: GestureDetector(
        onTap: _onTap,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Container(
              width: widget.width,
              height: _currentMode == RichTextDisplayMode.compact ? null : _heightAnimation.value,
              margin: widget.margin,
              padding: widget.padding,
              decoration: BoxDecoration(
                color: _colorAnimation.value ?? effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border: widget.hasBorder
                    ? Border.all(
                        color: effectiveBorderColor,
                        width: widget.borderWidth,
                      )
                    : null,
                boxShadow: widget.hasShadow
                    ? [
                        BoxShadow(
                          color: effectiveShadowColor.withAlpha(26),
                          blurRadius: widget.elevation,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: _currentMode == RichTextDisplayMode.compact
                  ? _buildCompactContent()
                  : _buildHoverContent(),
            );
          },
        ),
      ),
    );
  }
}

/// Expanded rich text editor dialog
class _ExpandedRichTextEditor extends StatefulWidget {
  final String initialText;
  final Function(String) onTextChanged;
  final bool isDarkTheme;
  final double fontSize;
  final String? fontFamily;
  final bool showBoldButton;
  final bool showItalicButton;
  final bool showUnderlineButton;
  final bool showStrikethroughButton;
  final bool showColorPicker;
  final bool showFontSizePicker;
  final bool showAlignmentButtons;
  final bool showBulletList;
  final bool showNumberedList;
  final bool showLinkButton;
  final bool showClearFormattingButton;

  const _ExpandedRichTextEditor({
    required this.initialText,
    required this.onTextChanged,
    this.isDarkTheme = false,
    this.fontSize = 16.0,
    this.fontFamily,
    this.showBoldButton = true,
    this.showItalicButton = true,
    this.showUnderlineButton = true,
    this.showStrikethroughButton = true,
    this.showColorPicker = true,
    this.showFontSizePicker = true,
    this.showAlignmentButtons = true,
    this.showBulletList = true,
    this.showNumberedList = true,
    this.showLinkButton = true,
    this.showClearFormattingButton = true,
  });

  @override
  State<_ExpandedRichTextEditor> createState() => _ExpandedRichTextEditorState();
}

class _ExpandedRichTextEditorState extends State<_ExpandedRichTextEditor> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  String _currentText = '';
  int _wordCount = 0;

  // Current formatting state
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderlined = false;
  bool _isStrikethrough = false;
  Color _currentColor = Colors.black;
  double _currentFontSize = 16.0;
  TextAlign _currentTextAlign = TextAlign.start;
  bool _isBulletList = false;
  bool _isNumberedList = false;

  @override
  void initState() {
    super.initState();
    _currentText = widget.initialText;
    _controller = TextEditingController(text: _currentText);
    _focusNode = FocusNode();
    _currentColor = widget.isDarkTheme ? Colors.white : Colors.black;
    _currentFontSize = widget.fontSize;
    _updateWordCount();

    _controller.addListener(_handleTextChange);
  }

  @override
  void dispose() {
    _controller.removeListener(_handleTextChange);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _handleTextChange() {
    final newText = _controller.text;
    if (_currentText != newText) {
      setState(() {
        _currentText = newText;
        _updateWordCount();
      });
      widget.onTextChanged(newText);
    }
  }

  void _updateWordCount() {
    _wordCount = _currentText.trim().isEmpty ? 0 : _currentText.trim().split(RegExp(r'\s+')).length;
  }

  void _toggleBold() {
    setState(() {
      _isBold = !_isBold;
    });
  }

  void _toggleItalic() {
    setState(() {
      _isItalic = !_isItalic;
    });
  }

  void _toggleUnderline() {
    setState(() {
      _isUnderlined = !_isUnderlined;
    });
  }

  void _toggleStrikethrough() {
    setState(() {
      _isStrikethrough = !_isStrikethrough;
    });
  }

  void _changeColor(Color color) {
    setState(() {
      _currentColor = color;
    });
  }

  void _changeFontSize(double size) {
    setState(() {
      _currentFontSize = size;
    });
  }

  void _changeAlignment(TextAlign align) {
    setState(() {
      _currentTextAlign = align;
    });
  }

  void _toggleBulletList() {
    setState(() {
      _isBulletList = !_isBulletList;
      _isNumberedList = false;
    });
  }

  void _toggleNumberedList() {
    setState(() {
      _isNumberedList = !_isNumberedList;
      _isBulletList = false;
    });
  }

  void _addLink() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Link'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: 'Text',
                hintText: 'Enter link text',
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              decoration: const InputDecoration(
                labelText: 'URL',
                hintText: 'Enter URL',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Add link implementation would go here
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _clearFormatting() {
    setState(() {
      _isBold = false;
      _isItalic = false;
      _isUnderlined = false;
      _isStrikethrough = false;
      _currentColor = widget.isDarkTheme ? Colors.white : Colors.black;
      _currentFontSize = widget.fontSize;
      _isBulletList = false;
      _isNumberedList = false;
    });
  }

  Widget _buildTopToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      decoration: BoxDecoration(
        color: widget.isDarkTheme ? Colors.grey.shade800 : Colors.grey.shade100,
        border: Border(
          bottom: BorderSide(
            color: widget.isDarkTheme ? Colors.grey.shade600 : Colors.grey.shade300,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            'Arial',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.w500,
              color: widget.isDarkTheme ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(width: 16.0),
          DropdownButton<double>(
            value: _currentFontSize,
            items: [12.0, 14.0, 16.0, 18.0, 20.0, 24.0].map((size) {
              return DropdownMenuItem(
                value: size,
                child: Text('${size.toInt()}'),
              );
            }).toList(),
            onChanged: (size) {
              if (size != null) _changeFontSize(size);
            },
          ),
          const SizedBox(width: 16.0),
          if (widget.showBoldButton)
            _buildToolbarButton(Icons.format_bold, _isBold, _toggleBold),
          if (widget.showItalicButton)
            _buildToolbarButton(Icons.format_italic, _isItalic, _toggleItalic),
          if (widget.showUnderlineButton)
            _buildToolbarButton(Icons.format_underlined, _isUnderlined, _toggleUnderline),
          if (widget.showStrikethroughButton)
            _buildToolbarButton(Icons.strikethrough_s, _isStrikethrough, _toggleStrikethrough),
          const SizedBox(width: 8.0),
          if (widget.showColorPicker)
            PopupMenuButton<Color>(
              icon: Icon(
                Icons.format_color_text,
                color: _currentColor,
              ),
              tooltip: 'Text Color',
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: Colors.black,
                  child: _ColorItem(color: Colors.black, name: 'Black'),
                ),
                PopupMenuItem(
                  value: Colors.red,
                  child: _ColorItem(color: Colors.red, name: 'Red'),
                ),
                PopupMenuItem(
                  value: Colors.blue,
                  child: _ColorItem(color: Colors.blue, name: 'Blue'),
                ),
                PopupMenuItem(
                  value: Colors.green,
                  child: _ColorItem(color: Colors.green, name: 'Green'),
                ),
                PopupMenuItem(
                  value: Colors.orange,
                  child: _ColorItem(color: Colors.orange, name: 'Orange'),
                ),
                PopupMenuItem(
                  value: Colors.purple,
                  child: _ColorItem(color: Colors.purple, name: 'Purple'),
                ),
              ],
              onSelected: _changeColor,
            ),
          const SizedBox(width: 8.0),
          if (widget.showAlignmentButtons)
            PopupMenuButton<TextAlign>(
              icon: const Icon(Icons.format_align_left),
              tooltip: 'Text Alignment',
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: TextAlign.left,
                  child: Row(
                    children: [
                      Icon(Icons.format_align_left),
                      SizedBox(width: 8.0),
                      Text('Left'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: TextAlign.center,
                  child: Row(
                    children: [
                      Icon(Icons.format_align_center),
                      SizedBox(width: 8.0),
                      Text('Center'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: TextAlign.right,
                  child: Row(
                    children: [
                      Icon(Icons.format_align_right),
                      SizedBox(width: 8.0),
                      Text('Right'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: TextAlign.justify,
                  child: Row(
                    children: [
                      Icon(Icons.format_align_justify),
                      SizedBox(width: 8.0),
                      Text('Justify'),
                    ],
                  ),
                ),
              ],
              onSelected: _changeAlignment,
            ),
          if (widget.showBulletList)
            _buildToolbarButton(Icons.format_list_bulleted, _isBulletList, _toggleBulletList),
          if (widget.showNumberedList)
            _buildToolbarButton(Icons.format_list_numbered, _isNumberedList, _toggleNumberedList),
          if (widget.showLinkButton)
            _buildToolbarButton(Icons.link, false, _addLink),
          if (widget.showClearFormattingButton)
            _buildToolbarButton(Icons.format_clear, false, _clearFormatting),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
            tooltip: 'Close',
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton(IconData icon, bool isActive, VoidCallback onPressed) {
    return IconButton(
      icon: Icon(icon),
      onPressed: onPressed,
      color: isActive ? Colors.blue : null,
      tooltip: icon.toString(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(16.0),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: widget.isDarkTheme ? Colors.grey.shade900 : Colors.white,
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Column(
          children: [
            _buildTopToolbar(),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  style: TextStyle(
                    color: widget.isDarkTheme ? Colors.white : Colors.black,
                    fontSize: _currentFontSize,
                    fontWeight: _isBold ? FontWeight.bold : FontWeight.normal,
                    fontStyle: _isItalic ? FontStyle.italic : FontStyle.normal,
                    decoration: _getTextDecoration(),
                    fontFamily: widget.fontFamily,
                  ),
                  textAlign: _currentTextAlign,
                  maxLines: null,
                  expands: true,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: 'Start typing...',
                    hintStyle: TextStyle(
                      color: widget.isDarkTheme ? Colors.grey.shade400 : Colors.grey.shade600,
                    ),
                  ),
                  autofocus: true,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              decoration: BoxDecoration(
                color: widget.isDarkTheme ? Colors.grey.shade800 : Colors.grey.shade100,
                border: Border(
                  top: BorderSide(
                    color: widget.isDarkTheme ? Colors.grey.shade600 : Colors.grey.shade300,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    '$_wordCount Words',
                    style: TextStyle(
                      color: widget.isDarkTheme ? Colors.grey.shade400 : Colors.grey.shade600,
                      fontSize: 12.0,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.keyboard_arrow_down),
                    onPressed: () => Navigator.of(context).pop(),
                    iconSize: 20.0,
                    tooltip: 'Minimize',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  TextDecoration _getTextDecoration() {
    if (_isUnderlined && _isStrikethrough) {
      return TextDecoration.combine([
        TextDecoration.underline,
        TextDecoration.lineThrough,
      ]);
    } else if (_isUnderlined) {
      return TextDecoration.underline;
    } else if (_isStrikethrough) {
      return TextDecoration.lineThrough;
    }
    return TextDecoration.none;
  }
}

/// Color item widget for color picker
class _ColorItem extends StatelessWidget {
  final Color color;
  final String name;

  const _ColorItem({
    required this.color,
    required this.name,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.grey),
          ),
        ),
        const SizedBox(width: 8),
        Text(name),
      ],
    );
  }
}
